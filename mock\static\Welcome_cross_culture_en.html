<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CognitiveTest - Home</title>
  <style>
    body,
    html {
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #f5f5f5;
      /* 浅灰色背景 */
      color: #333333;
      /* 深灰色文本 */
      text-align: center;
    }

    .header {
      padding: 40px 20px;
      background-color: #76323F;
      /* 深蓝色标题栏 */
      color: white;
      text-align: center;
      /* height: 100%; */
      /* max-height: 1000px; */
    }

    .header h1 {
      font-size: 2em;
    }

    .header p {
      /* font-size: 1.5em; */
      text-align: center;
    }


    h2 {
      font-size: 18px;
      font-weight: bold;
      text-align: left;
    }

    body {
      line-height: 1.6;
      margin: 0px;
    }

    p {
      text-align: left;
      margin-top: 1em;
    }



    .login-form {
      background: white;
      padding: 10px;
      border-radius: 8px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
      width: 100%;
      max-width: 550px;
      margin: 20px auto;
      margin-top: 1em;
      transition: all 0.3s ease-in-out;
    }

    .form-group {
      font-size: 18px;
      margin-bottom: 15px;
    }



    .submit-button {
      background-color: rgb(30, 144, 255);
      color: #FFFFFF;
      border: none;
      padding: 15px 30px;
      font-size: 1.2em;
      font-weight: bold;
      border-radius: 10px;
      cursor: pointer;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s, box-shadow 0.3s;
      width: 100%;
      display: flex;
      /* 使用Flex布局 */
      justify-content: center;
      /* 水平居中 */
      align-items: center;
      /* 垂直居中 */
      margin-top: 60px;
    }

    .submit-button:hover {
      transform: scale(1.05);
      box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
    }
    
    /* Media query for smaller screens */
@media only screen and (max-width: 600px) {
  .form-group input,
  .form-group select {
    width: 90%; /* Adjust width to fit smaller screens */
    max-width: 90%; /* Ensure inputs don't exceed screen width */
  }
  .header {
      width: 90%; /* Adjust width to fit smaller screens */
    max-width: 90%; /* Ensure inputs don't exceed screen width */
  }
  .login-form {
      width: 90%; /* Adjust width to fit smaller screens */
    max-width: 90%; /* Ensure inputs don't exceed screen width */
  }

  }
  </style>

  <script>
    function validateForm() {

      window.location.href = './Inform_cross_culture_en.html';
      //window.location.href = './TaskSequence.html?idNumber=' + encodeURIComponent(idNumber);

      return false; // 防止表单的默认提交行为
    }
  </script>

</head>

<body>
  <!-- <img src="SanboLogo.PNG" width="100" height="25" style="float: left;"> -->
  <div class="header">
    <h1> Welcome to the Language Skills and Association Test</h1>
    <p></p>
  </div>

  <form class="login-form" onsubmit="return validateForm()">
    <div class="form-group" style="text-indent: 1em;">


      <p>This test aims to assess and compare your language expression and associative ability in both Chinese and English environments.</p>
      <p>All of your personal information will be kept strictly confidential and used solely for this research study.</p>
      <p>The entire experiment is expected to take 35 minutes to complete. Please finish the experiment in one session; if you exit midway, you will not be eligible for the experiment compensation.</p>
      <p>The tasks require continuous audio recording and a stable internet connection. Please ensure you are in a quiet, connected environment to maintain clear audio quality and successful data upload.</p>
      <p>For any questions, feel free to contact the research team via email: <EMAIL>.</p>
      <div style="display: flex; justify-content: space-between;">
        <p style="margin: 0;font-size: smaller;"><a href="./Informed_Consent_cross_culture_en.html">Privacy protection and Informed Consent Statement for participation in cognitive testing</a></p>
      </div>


    </div>
    <button type="submit" class="submit-button" id="submit-button">I Agree, Let's Start</button>
  </form>


</body>

</html>