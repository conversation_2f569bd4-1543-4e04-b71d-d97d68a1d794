<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <title>Discourse</title>
    <link rel="stylesheet" href="dis_crossculture.css">
    <script src="audio-utils.js"></script>

     
    <style>
        /* 添加样式以美化可视化画布 */
        #audioVisualizer {
            width: 100%;
            height: 100px;
            background-color: #f3f3f3;
            border: 1px solid #ccc;
            margin-top: 20px;
            display: none; /* 初始隐藏，录音时显示 */
        }
        /* Style for the English reminder */
        .english-reminder {
            background-color: #ffcc00;
            color: #000;
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 20px;
            margin-bottom: 15px;
            display: inline-block;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
    </style>
</head>

<body>
<div class="progress-container" >
    <div class="progress-bar" id="progress_bar" style="width:70%;">70%</div>
</div>
    <div class="container">
        <!-- Added English reminder at the top of the container -->
        <div class="english-reminder">PLEASE ANSWER IN ENGLISH</div>
        


        <p id="questionText" class="question"><span class="highlight">请简单介绍一下自己</span>。<br>
            请在准备好回答之后点击"开始录音"（回答时间需要超过<span id="answerTime" class="key-point">1分钟</span>）</p>

        <button id="startRecord" class="btn">Start</button>
        <button id="stopRecord" class="btn" disabled>Stop</button>
        <audio id="audio" controls class="audio-player"></audio>
        <p id="status" class="status-text">Awaiting recording</p>
        <button id="nextQuestion" class="btn" disabled>Next task</button>
        <button id="skipQuestion" style="display:none" class="btn" disabled>跳过</button> <!-- [change] 跳过省略 -->
        <!-- <button id="reUpload" style="display:none" class="btn" disabled>上传重试</button> -->
        <button id="reUpload" class="btn" disabled>Upload(retry)</button>
        <!-- <button id="reskip" style="display:none" class="btn" disabled>切换重试</button> -->
        <button id="reskip" class="btn" disabled>Next task(retry)</button>
        <div id="savingStatus" style="display: none;">正在保存...</div>
        <!-- 添加音频可视化画布 -->
        <canvas id="audioVisualizer" width="600" height="100"></canvas>
    </div>

    <script>
        //var idNumber = 'yjhtest';
        var idNumber = decodeURIComponent(location.search.match(/[^\?&]*idNumber=([^&]*)/)[1]);
        //读取存储的currentIndex0804,可以刷新
        var currentQuestionIndex = JSON.parse(sessionStorage.getItem('dis_currentQuestionIndex_storage'));
        if (currentQuestionIndex) {
            console.log('Read index successfully');
        } else {
            var currentQuestionIndex = 0;
            console.log('Initial currentIndex');
        }
        let questions = [
            { text: "<span style='color: #d9534f; font-size:30px'><strong>Please briefly introduce yourself</strong></span>", time: 60 },//60
            { text: "<span style='color: #d9534f; font-size:30px'><strong>Please describe and evaluate your current work or studies. You may talk about your role, challenges, and your perspective on your current situation.</strong></span>", time: 60 },//60
            { text: "<span style='color: #d9534f; font-size:30px'><strong>Please describe the city you currently live in. You can talk about its features, strengths, or weaknesses, and your overall opinion of the city.</strong></span>", time: 60 },//60
            { text: "<span style='color: #d9534f; font-size:30px'><strong>Please describe your family background, including your relationships with family members, the family atmosphere, and your overall opinion of your family.</strong></span>", time: 60 },//60
            { text: "<span style='color: #d9534f; font-size:30px'><strong>Please describe and evaluate your past romantic experiences. If you have no romantic experience, please describe your ideal partner.</strong></span>", time: 60 },//60
            { text: "<span style='color: #d9534f; font-size:30px'><strong>Please describe the person who raised you and evaluate your relationship with them.</strong></span>", time: 60 },//60
            // { text: "<span style='color: #d9534f; font-size:30px'><strong>请描述并评价您和后辈的关系</strong></span>", time: 60 },//60
            { text: "<span style='color: #d9534f; font-size:30px'><strong>Please choose one or more groups (peers, colleagues, or friends) and describe and evaluate your relationship with them. You may discuss the frequency of interactions, mutual support, and your feelings toward each other.</strong></span>", time: 60 },//60
            { text: "<span style='color: #d9534f; font-size:30px'><strong>Please describe and evaluate the most impactful event in your life, and how it has influenced your views, actions, or way of living.</strong></span>", time: 60 },//60
            // { text: "<span style='color: #d9534f; font-size:30px'><strong>请描述并评价您的反复做的相同的梦。如果没有，请描述并评价您最近做过的梦</strong></span>", time: 60 },//60
        ];
        

        let startRecord = document.getElementById('startRecord');
        let stopRecord = document.getElementById('stopRecord');
        let audio = document.getElementById('audio');
        let statusText = document.getElementById('status');
        let nextQuestion = document.getElementById('nextQuestion');
        let skipQuestion = document.getElementById('skipQuestion');
        let questionText = document.getElementById('questionText');
        let answerTime = document.getElementById('answerTime');

        let reUpload = document.getElementById('reUpload');
        let reskip = document.getElementById('reskip');
        let mediaRecorder;
        let audioChunks = [];
        let recordingDuration = 0;
        let recordingInterval;
        let skip_onclicked = false;

        let progress_bar=document.getElementById('progress_bar');

        // 音频可视化相关变量
        let audioVisualizer = document.getElementById('audioVisualizer');
        let canvasCtx = audioVisualizer.getContext('2d');
        let audioContext;
        let analyser;
        let source;
        let animationId;

        showQuestion(); // Initialize with the first question
        function showQuestion() {
            skip_onclicked = false
            reUpload.disabled = true;
            reskip.disabled = true;
            nextQuestion.disabled = true;
            skipQuestion.disabled = true;
            startRecord.disabled = false;
            if (currentQuestionIndex < questions.length) {
                let width_progress=100*(currentQuestionIndex+1)/questions.length;
                progress_bar.style.width=width_progress+'%';
                progress_bar.innerHTML=`${currentQuestionIndex+1}/${questions.length}`;

                // Modified to include a reminder about answering in English
                questionText.innerHTML = questions[currentQuestionIndex].text + '</span> <br><br><span style="color: #ffcc00; font-weight: bold; font-size: 18px;"> </span><br><br>Click "Start " and speak for at least <span class="key-point">' + questions[currentQuestionIndex].time + 'seconds</span>.<br>Once the time is reached, the system will allow you to stop the recording.';

                currentQuestionIndex++;
                nextQuestion.disabled = true; // Disable next question button until audio is uploaded
                startRecord.textContent = "Start";
            } else {
                window.location.href = 'cross_culture_tat_intro_en.html?idNumber=' + encodeURIComponent(idNumber);  // [change] 改跳转
            }
        }

        document.addEventListener('DOMContentLoaded', function () {
            skipQuestion.disabled = true;
            startRecord.onclick = function () {
                setTimeout(()=>{stopRecord.disabled = false;}, 1000*questions[currentQuestionIndex - 1].time);

                skipQuestion.disabled = false;
                //隐藏录音进度条
                audio.style.display = 'none';
                audioChunks = [];
                audio.src = '';
                navigator.mediaDevices.getUserMedia({ audio: true })
                    .then(stream => {
                        let options = {};
                        if (MediaRecorder.isTypeSupported('audio/webm')) {
                            options.mimeType = 'audio/webm';
                        } else {
                            console.log('Using browser default audio format.');
                        }
                        mediaRecorder = new MediaRecorder(stream, options);
                        mediaRecorder.ondataavailable = function (e) {
                            audioChunks.push(e.data);
                        };
                        mediaRecorder.onstop = function () {
                            let audioBlob = new Blob(audioChunks, { type: mediaRecorder.mimeType });
                            let audioUrl = URL.createObjectURL(audioBlob);
                            audio.src = audioUrl;
                            clearInterval(recordingInterval);

                            // 停止可视化
                            if (animationId) {
                                cancelAnimationFrame(animationId);
                            }
                            if (audioContext) {
                                audioContext.close();
                            }
                            audioVisualizer.style.display = 'none';

                            if (skip_onclicked == true) {
                                skipQuestion.disabled = true;
                                startRecord.disabled = true;
                                stopRecord.disabled = true;
                                statusText.textContent = "Recording has stopped";
                                nextQuestion.disabled = true;
                                audio.style.display = 'block';
                                nextQuestion.disabled = true;
                                reUpload.disabled = true;
                                let audioBlob = new Blob(audioChunks, { type: mediaRecorder.mimeType });
                                uploadAudio(audioBlob).then((result) => {
                                    showQuestion();
                                });
                            }
                            // uploadAudio(audioBlob); // Call upload function on stop
                        };
                        mediaRecorder.start();         

                        // 初始化音频可视化
                        audioVisualizer.style.display = 'block';
                        audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        source = audioContext.createMediaStreamSource(stream);
                        analyser = audioContext.createAnalyser();
                        analyser.fftSize = 2048;
                        source.connect(analyser);

                        const bufferLength = analyser.frequencyBinCount;
                        const dataArray = new Uint8Array(bufferLength);

                        // 设置 canvas 的实际尺寸，以匹配 CSS 尺寸
                        audioVisualizer.width = audioVisualizer.clientWidth;
                        audioVisualizer.height = audioVisualizer.clientHeight;

                        // 清除画布
                        canvasCtx.clearRect(0, 0, audioVisualizer.width, audioVisualizer.height);

                        function drawVisualizer() {
                            animationId = requestAnimationFrame(drawVisualizer);

                            analyser.getByteTimeDomainData(dataArray);

                            // 绘制背景
                            canvasCtx.fillStyle = '#f3f3f3';
                            canvasCtx.fillRect(0, 0, audioVisualizer.width, audioVisualizer.height);

                            // 绘制波形
                            canvasCtx.lineWidth = 2;
                            canvasCtx.strokeStyle = '#d9534f';

                            canvasCtx.beginPath();

                            const sliceWidth = audioVisualizer.width * 1.0 / bufferLength;
                            let x = 0;

                            for (let i = 0; i < bufferLength; i++) {
                                const v = dataArray[i] / 128.0;
                                const y = v * audioVisualizer.height / 2;

                                if (i === 0) {
                                    canvasCtx.moveTo(x, y);
                                } else {
                                    canvasCtx.lineTo(x, y);
                                }

                                x += sliceWidth;
                            }

                            canvasCtx.lineTo(audioVisualizer.width, audioVisualizer.height / 2);
                            canvasCtx.stroke();
                        }

                        drawVisualizer();

                        recordingDuration = 0;
                        recordingInterval = setInterval(function () {
                            recordingDuration++;
                            statusText.textContent = "Recording ｜ Time：" + recordingDuration + " sec";
                        }, 1000);
                        // stopRecord.disabled = false;
                        startRecord.disabled = true;
                        nextQuestion.disabled = true;
                        // startRecord.textContent = "重新录音";
                        statusText.textContent = "Recording started";
                    });
            };

            skipQuestion.onclick = function () {
                skip_onclicked = true;
                mediaRecorder.stop();

            }

            stopRecord.onclick = function () {
                if (recordingDuration < questions[currentQuestionIndex - 1].time) {
                    alert("The recording time is longer than " + questions[currentQuestionIndex - 1].time + " seconds, come on, please speak more.");
                    return;
                }

                nextQuestion.disabled = false;
                skipQuestion.disabled = true;
                mediaRecorder.stop();
                // startRecord.disabled = false;
                stopRecord.disabled = true;
                statusText.textContent = "Recording has stopped";
                //显示录音进度条
                audio.style.display = 'block';
            };

            nextQuestion.onclick = function () {

                nextQuestion.disabled = true;
                let audioBlob = new Blob(audioChunks, { type: mediaRecorder.mimeType });
                uploadAudio(audioBlob).then((result) => {
                    console.log('Promise 已解决，数据为：', result);
                    showQuestion();
                });
                statusText.textContent = "Saving...";
            };

            reskip.onclick = function () {

                showQuestion();
            };
            reUpload.onclick = function () {
                let audioBlob = new Blob(audioChunks, { type: mediaRecorder.mimeType });
                uploadAudio(audioBlob).then((result) => {
                    reskip.disabled = false;
                });

            };



            function uploadAudio(blob) {
                // Use the universal audio upload function
                return uploadAudioUniversal(
                    blob,
                    idNumber,
                    String(currentQuestionIndex),
                    'dis',
                    currentQuestionIndex,
                    statusText
                ).then((result) => {
                    audio.style.display = 'none';
                    sessionStorage.setItem('dis_currentQuestionIndex_storage', currentQuestionIndex); // 0804
                    console.log('upload success', result);
                    reUpload.disabled = true;
                    nextQuestion.disabled = true; // fix property name
                    return result;
                }).catch((e) => {
                    statusText.textContent = "Failed to save";
                    alert(e.message || "Unknown error while uploading.");
                    console.warn(e);
                    reUpload.disabled = false;
                    throw e;
                });
            }

        });
    </script>


</body>

</html>