<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Language Test - Home</title>
  <style>
    body, html {
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #f5f5f5; /* 浅灰色背景 */
      color: #333333; /* 深灰色文本 */
      text-align: center;
    }
    .header {
      padding: 40px 20px;
      background-color: #76323F;
      color: white;
    }
    .header h1 {
      font-size: 2.5em;
      margin-bottom: 0.5em;
    }
    .header p {
      font-size: 1.2em;
    }
    
    .content {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 20px;
      padding: 20px;
    }
    .module {
      background-color: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      margin: 10px;
      width: 280px;
      cursor: pointer;
      transition: transform 0.3s, box-shadow 0.3s;
    }
    .module:hover {
      transform: scale(1.05);
      box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
    }
    .info-section {
      background-color: rgba(255, 255, 255, 0.8);
      color: #333;
      padding: 20px;
      border-radius: 8px;
      width: 80%;
      max-width: 500px;
      margin: 20px auto;
      transition: transform 0.3s;
    }
    .info-section:hover {
      transform: scale(1.02);
    }
    .footer {
      background: #565656;
      color: white;
      padding: 15px 0;
      position: fixed;
      bottom: 0;
      width: 100%;
    }


    #button {
      padding: 20px 40px;
      /* 设置按钮内边距 */
      font-size: 36px;
      /* 设置按钮字体大小 */
      background-color: #FFF;
      /* 设置按钮背景颜色 */
      color: black;
      /* 设置按钮文字颜色 */
      border: none;
      /* 去掉按钮边框 */
      border-radius: 30px;
      /* 设置按钮圆角 */
      cursor: pointer;
      /* 鼠标悬停时显示手型 */
      transition: background-color 0.3s;
      /* 设置按钮背景颜色变化的过渡效果 */
    }

    #button:hover {
      background-color: #76323F;
      /* 设置鼠标悬停时的按钮背景颜色 */
    }
  </style>
</head>
<body>

<div class="header">
  <h1>Choose a test to start your session</h1>
</div>



<div class="content">

<!--  <div class="module" onclick="location.href='cross_culture_vft_intro_en.html?idNumber='+ encodeURIComponent(idNumber)">-->
  <div id="vft_card" class="module" onclick="jump_to_vft()">
    <h2>VFT Task (Word Association)</h2>
    <p style="color: gray">Perform word associations in both Chinese and English under 3 themes, with 5 minutes allocated for each theme.</p>
  </div>

  <div id="tat_card" class="module" onclick="jump_to_tat()">
    <h2>TAT Task (Picture Narration)</h2>
    <p style="color: gray">Narrate stories based on 8 pictures in both Chinese and English, with at least 2 minutes for each picture.</p>
  </div>
  
</div>


<!-- <div class="footer">
  &copy; 2024 Language test
</div> -->
<script>
  function jump_to_vft(){
    if(task==null){
      location.href='test_audio_device_en.html?task=VFT&idNumber='+ encodeURIComponent(idNumber);
    }else{
      window.location.href='cross_culture_vft_intro_en.html?idNumber='+ encodeURIComponent(idNumber);
    }

  }
  function jump_to_tat(){
    if(task==null) {
      location.href = 'test_audio_device_en.html?task=TAT&idNumber=' + encodeURIComponent(idNumber);
    }else{
      window.location.href = 'dis_seq_cross_culture_en.html?idNumber=' + encodeURIComponent(idNumber);
    }
  }
  //var idNumber =location.search.replace(/[^\d]/g,"");
  var idNumber = decodeURIComponent(location.search.match(/[^\?&]*idNumber=([^&]*)/)[1]);

  const url = new URL(window.location);
  const params = new URLSearchParams(url.search);
  const task = params.get('back_from');
  console.log(task);

  if(task=='tat'){
    let card=document.getElementById('tat_card');
    card.style.display='none';
    console.log(card)
  }
  if(task=='vft'){
    let card=document.getElementById('vft_card');
    card.style.display='none';
    console.log(card)
  }

</script>
</body>
</html>