#!/usr/bin/env python3
"""
Build script to create a standalone executable from server.py
"""

import os
import sys
import subprocess
import shutil

def install_pyinstaller():
    """Install PyInstaller if not already installed"""
    try:
        import PyInstaller
        print("PyInstaller is already installed")
    except ImportError:
        print("Installing PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])

def build_executable():
    """Build the executable using PyInstaller"""
    print("Building executable...")
    
    # Clean previous builds
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    if os.path.exists("build"):
        shutil.rmtree("build")
    
    # Build command
    cmd = [
        "pyinstaller",
        "--onefile",  # Create a single executable file
        # Removed --windowed flag to show console window
        "--name=server",  # Name of the executable
        "--exclude-module=PyQt6",  # Exclude PyQt6 to avoid conflict with PyQt5
        "server.py"  # Script to build
    ]
    
    # Run PyInstaller
    subprocess.check_call(cmd)

    print(f"Executable created in: {os.path.abspath('dist/server.exe')}")

def copy_resources():
    """Copy static folder to dist directory, data folder will be created at runtime"""
    print("Copying static folder to dist directory...")

    # Copy static folder
    if os.path.exists("static"):
        dest_static = os.path.join("dist", "static")
        if os.path.exists(dest_static):
            shutil.rmtree(dest_static)
        shutil.copytree("static", dest_static)
        print(f"Copied static folder to: {os.path.abspath(dest_static)}")
    else:
        print("Warning: static folder not found!")

    print("Note: data folder will be created automatically when the server runs.")
    print("Static folder copied successfully!")

def main():
    """Main function"""
    print("Building standalone executable for audio upload server...")
    
    # Install requirements
    install_requirements()
    
    # Install PyInstaller
    install_pyinstaller()
    
    # Build executable
    build_executable()

    # Copy resource folders
    copy_resources()

    print("\nBuild completed successfully!")
    print("You can now run the server by double-clicking on dist/server.exe")
    print("The data and static folders are located next to the executable.")

if __name__ == "__main__":
    main()