#!/usr/bin/env python3
"""
Test script to simulate deployment environment and verify file structure
"""

import os
import sys
import tempfile
import shutil

def simulate_deployment_test():
    """Simulate the deployment environment"""
    print("Deployment Simulation Test")
    print("=" * 50)
    
    # Create a temporary directory to simulate deployment
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"Simulating deployment in: {temp_dir}")
        
        # Copy server.exe simulation (just copy the script)
        server_path = os.path.join(temp_dir, "server.py")
        shutil.copy("server.py", server_path)
        
        # Copy static folder
        static_src = "static"
        static_dest = os.path.join(temp_dir, "static")
        if os.path.exists(static_src):
            shutil.copytree(static_src, static_dest)
            print(f"✓ Static folder copied to: {static_dest}")
        else:
            print("✗ Static folder not found!")
            return False
        
        # Change to deployment directory
        original_cwd = os.getcwd()
        os.chdir(temp_dir)
        
        try:
            # Import and test the path resolution
            sys.path.insert(0, temp_dir)
            
            # Simulate the path resolution logic
            def get_resource_path(relative_path):
                if getattr(sys, 'frozen', False):
                    base_path = os.path.dirname(sys.executable)
                else:
                    base_path = os.path.abspath(".")
                return os.path.join(base_path, relative_path)
            
            # Test paths
            data_path = get_resource_path('data')
            static_path = get_resource_path('static')
            welcome_file = os.path.join(static_path, 'Welcome_cross_culture_en.html')
            
            print(f"\nPath Resolution in Deployment:")
            print(f"Data path: {data_path}")
            print(f"Static path: {static_path}")
            print(f"Welcome file: {welcome_file}")
            
            # Test data folder creation
            os.makedirs(data_path, exist_ok=True)
            print(f"✓ Data folder created: {os.path.exists(data_path)}")
            
            # Test static files access
            static_exists = os.path.exists(static_path)
            welcome_exists = os.path.exists(welcome_file)
            
            print(f"✓ Static folder exists: {static_exists}")
            print(f"✓ Welcome file exists: {welcome_exists}")
            
            if static_exists:
                files = os.listdir(static_path)
                print(f"Static folder contents: {files}")
            
            # Final structure check
            print(f"\nFinal deployment structure:")
            for root, dirs, files in os.walk("."):
                level = root.replace(".", "").count(os.sep)
                indent = " " * 2 * level
                print(f"{indent}{os.path.basename(root)}/")
                subindent = " " * 2 * (level + 1)
                for file in files:
                    print(f"{subindent}{file}")
            
            return static_exists and welcome_exists
            
        finally:
            os.chdir(original_cwd)
            sys.path.remove(temp_dir)

def main():
    success = simulate_deployment_test()
    print(f"\nDeployment test {'PASSED' if success else 'FAILED'}")
    return success

if __name__ == "__main__":
    main()
