<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CognitiveTest</title>
    <style>
        body,
        html {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            /* 浅灰色背景 */
            color: #333333;
            /* 深灰色文本 */
            text-align: center;
            font-size: 16px;
        }

        .header {
            padding: 40px 20px;
            background-color: #76323F;
            /* 深蓝色标题栏 */
            color: white;
        }

        .header h1 {
            font-size: 3em;
        }

        .header p {
            font-size: 1.5em;
        }

        .h2 {
            padding: 10px 5px;
            color: white;
            font-size: 1.8em;
            margin-bottom: 0.5em;
        }

        .login-form {
            background: white;
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 550px;
            margin: 20px auto;
            transition: all 0.3s ease-in-out;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: bold;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background: #f7f7f7;
            cursor: pointer;
            font-size: 16px;
            color: gray;
            width: 500px;
            /* 设置所有输入框宽度为200像素 */
        }

        .submit-button {
            background-color: rgb(30, 144, 255);
            color: #FFFFFF;
            border: none;
            padding: 15px 30px;
            font-size: 1.5em;
            font-weight: bold;
            border-radius: 10px;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            width: 100%;
            display: flex;
            /* 使用Flex布局 */
            justify-content: center;
            /* 水平居中 */
            align-items: center;
            /* 垂直居中 */
            margin-top: 60px;
        }

        .second_button {
            background-color: rgb(30, 144, 255);
            color: #FFFFFF;
            border: none;
            padding: 15px 30px;
            font-size: 1.5em;
            font-weight: bold;
            border-radius: 10px;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            width: 30%;
            display: flex;
            /* 使用Flex布局 */
            justify-content: center;
        }

        .submit-button:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
        }

        .footer {
            background: #565656;
            /* 深灰色页脚 */
            color: white;
            padding: 15px 0;
            position: fixed;
            bottom: 0;
            width: 100%;
        }

        .form-group input::placeholder {
            color: #999;
            /* 或者其他灰色色值 */
        }

        /* Media query for smaller screens */
        @media only screen and (max-width: 600px) {

            .form-group input,
            .form-group select {
                width: 90%;
                /* Adjust width to fit smaller screens */
                max-width: 90%;
                /* Ensure inputs don't exceed screen width */
            }

            .header {
                width: 90%;
                /* Adjust width to fit smaller screens */
                max-width: 90%;
                /* Ensure inputs don't exceed screen width */
            }

            .login-form {
                width: 90%;
                /* Adjust width to fit smaller screens */
                max-width: 90%;
                /* Ensure inputs don't exceed screen width */
            }
        }

        .upload-container {
            position: relative;
            width: 150px;
            text-align: center;
        }
    </style>
    <script src="./Bmob.js"></script>
     
    <script>
        //Bmob.initialize("6943fce1f5470912", "1234567891011121");
        Bmob.initialize("28ce3264d03ab5aa", "1234567891123456");
        function getFileExtension(mimeType) {
            switch (mimeType) {
                case 'image/jpeg':
                    return '.jpg';
                case 'image/png':
                    return '.png';
                case 'image/gif':
                    return '.gif';
                case 'image/webp':
                    return '.webp';
                case 'image/bmp':
                    return '.bmp';
                case 'image/tiff':
                    return '.tiff';
                case 'image/svg+xml':
                    return '.svg';
                // 添加其他支持的图片格式
                default:
                    return ''; // 如果MIME类型不匹配任何已知的图片类型，返回空字符串
            }
        }

        function test(objectName, uploadFile) {
            console.log('执行');
            var client = new AliOSSWebUploader({
                region: "oss-cn-beijing",
                bucket: "liulab",
                accessKeyId: "LTAI5tAwuqyPDrUmK3JAsKWg",
                accessKeySecret: "******************************",
            });
            return new Promise((resolve, reject) => {
                client.postObject(objectName, uploadFile, {
                    'x-oss-object-acl': 'public-read',
                    'success_action_status': 200,
                    timeout: 30000,
                    onProgress: function (e) {
                        console.log('complete', e.percent.toFixed(2), '%');
                    },
                    onSuccess: function () {
                        var url = client.generateObjectUrl(objectName);
                        //$(".result").innerHTML = 'Object url: <a target="_blank" href="' + url + '">' + url + '</a>';
                        console.log('upload success');
                        resolve(); // Resolve the promise on success
                        // reUpload.disabled = true;
                        // nextQuestion.disabled = true;
                    },
                    onError: function (e) {
                        alert(e.message || "Unknown error, maybe some value is wrong.");
                        console.warn(e);
                        // nextQuestion.disabled = true;
                        // skipQuestion.disabled = true;
                        // reUpload.disabled = false;
                        reject(e); // Reject the promise on error
                    }
                });
            });
        }
    </script>
    <script>
        window.onload = function () {
            // 检查本地存储是否有保存的数据，并填充到表单中
            var storedData = JSON.parse(localStorage.getItem('formData'));
            if (storedData) {
                document.getElementById('name').value = storedData.name || '';
                document.getElementById('chinese_ability').value = storedData.chinese_ability || "";
                document.getElementById('age').value = storedData.age || '';
                document.getElementById('gender').value = storedData.gender || '';
                document.getElementById('nationality').value = storedData.nationality || '';
                document.getElementById('Ethnicity').value = storedData.Ethnicity || '';
                document.getElementById('occupation').value = storedData.occupation || '';
                document.getElementById('work_unit').value = storedData.work_unit || '';
                document.getElementById('PhoneNum').value = storedData.PhoneNum || '';
                document.getElementById('email').value = storedData.email || '';
                document.getElementById('HSK').value = storedData.HSK || '';
                document.getElementById('education').value = storedData.education || '';
                document.getElementById('abroad').value = storedData.abroad || '';
            }
        };

        function validateForm() {
            var name = document.getElementById("name").value;
            var chinese_ability = document.getElementById("chinese_ability").value;
            var age = document.getElementById("age").value;
            var gender = document.getElementById("gender").value;
            var nationality = document.getElementById("nationality").value;
            var Ethnicity = document.getElementById("Ethnicity").value;
            var occupation = document.getElementById("occupation").value;
            var work_unit = document.getElementById("work_unit").value;
            var PhoneNum = document.getElementById("PhoneNum").value;
            var email = document.getElementById("email").value;
            var HSK = document.getElementById("HSK").value;
            var education = document.getElementById("education").value;
            var abroad = document.getElementById("abroad").value;

            // Use PhoneNum as the idNumber
            var idNumber = PhoneNum;

            if (name === "" || chinese_ability === "" || age === "" || gender === "" || nationality === "" || Ethnicity === ""
                || occupation === "" || work_unit === "" || PhoneNum === "" || email === "" || HSK === ""
                || education === "" || abroad === ""
            ) {
                alert("All fields are required");
                return false;
            }

            // 存储数据
            var SubjectInformation = Bmob.Query("CrossCultureInfo");
            SubjectInformation.set("name", name);
            SubjectInformation.set("idNumber", idNumber);
            SubjectInformation.set("chinese_ability", chinese_ability);
            SubjectInformation.set("age", age);
            SubjectInformation.set("gender", gender);
            SubjectInformation.set("nationality", nationality);
            SubjectInformation.set("Ethnicity", Ethnicity);
            SubjectInformation.set("occupation", occupation);
            SubjectInformation.set("work_unit", work_unit);
            SubjectInformation.set("PhoneNum", PhoneNum);
            SubjectInformation.set("email", email);
            SubjectInformation.set("HSK", HSK);
            SubjectInformation.set("education", education);
            SubjectInformation.set("abroad", abroad);

            SubjectInformation.save().then(res => {
                console.log("数据保存成功", res);
                window.location.href = './LanguageOnlineNew/cross_culture_index_en.html?idNumber=' + encodeURIComponent(idNumber + 'CrossCulture');
            }).catch(err => {
                console.error("数据保存失败", err);
            });

            // 存储数据到本地存储
            var formData = {
                name: name,
                idNumber: idNumber,
                chinese_ability: chinese_ability,
                age: age,
                gender: gender,
                nationality: nationality,
                Ethnicity: Ethnicity,
                occupation: occupation,
                work_unit: work_unit,
                PhoneNum: PhoneNum,
                email: email,
                HSK: HSK,
                education: education,
                abroad: abroad,
            };
            localStorage.setItem('formData', JSON.stringify(formData));

            return false; // 防止表单的默认提交行为
        }
    </script>
</head>

<body>
    <div class="header">
        <h1> Personal Information</h1>
        <div class="h2"></div>
        <h2> </h2>
    </div>

    <form class="login-form" onsubmit="return validateForm()">
        <!-- 基本信息 -->
        <div class="form-group">
            <label for="name">Name:</label>
            <input type="text" id="name" name="name" placeholder="English only">
        </div>

        <div class="form-group">
            <label for="PhoneNum">Phone number</label>
            <input type="number" id="PhoneNum" name="PhoneNum" pattern="\d*" placeholder="Please enter the number">
        </div>

        <div class="form-group">
            <label for="chinese_ability">Your Chinese Proficiency </label>
            <select id="chinese_ability" name="chinese_ability">
                <option value="">Please select</option>
                <option value="1">Beginner: Able to communicate and understand simple phrases and basic vocabulary.
                </option>
                <option value="2">Basic: Able to engage in basic communication, but with frequent mistakes and limited
                    fluency.</option>
                <option value="3">Intermediate: Able to communicate effectively in daily situations, with occasional
                    errors.</option>
                <option value="4">Advanced: Able to communicate fluently and accurately in most situations, with few
                    errors.</option>
                <option value="5">Proficient: Near-native fluency and accuracy; able to use the language effortlessly
                    across a wide range of contexts.</option>
            </select>
        </div>

        <div class="form-group">
            <label for="HSK">HSK Level( old 6-level system)</label>
            <select id="HSK" name="HSK">
                <option value="">Please Select</option>
                <option value="0">Have not taken the HSK exam</option>
                <option value="1">HSK (Level 4)</option>
                <option value="2">HSK (Level 5)</option>
                <option value="3">HSK (Level 6)</option>
            </select>
        </div>

        <div class="form-group">
            <label for="age">Age</label>
            <input type="number" id="age" name="age" pattern="\d*"
                oninput="if(!/^[0-9]+$/.test(value)) value=value.replace(/\D/g,'');if(value>100)value=100;if(value<0)value=null"
                placeholder="Please enter the number">
        </div>

        <div class="form-group">
            <label for="gender">Gender</label>
            <select id="gender" name="gender">
                <option value="">Please select</option>
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="others">Others</option>
            </select>
        </div>

        <div class="form-group">
            <label for="nationality">Nationality</label>
            <input type="text" id="nationality" name="nationality" placeholder="Please enter your nationality">
        </div>

        <div class="form-group">
            <label for="Ethnicity">Ethnicity</label>
            <select id="Ethnicity" name="Ethnicity">
                <option value="">Please Select</option>
                <option value="0">Asian or Asian American</option>
                <option value="1">Black or African American</option>
                <option value="2">Hispanic or Latino</option>
                <option value="3">White or Caucasian</option>
                <option value="4">Native American or Alaska Native</option>
                <option value="5">Native Hawaiian or Pacific Islander</option>
                <option value="6">Mixed or Multi-Ethnic</option>
                <option value="7">Prefer not to disclose</option>
            </select>
        </div>

        <div class="form-group">
            <label for="occupation">Grade/Occupation</label>
            <input type="text" id="occupation" name="occupation" placeholder="Please enter grade or work unit">
        </div>

        <div class="form-group">
            <label for="work_unit">School/Workplace</label>
            <input type="text" id="work_unit" name="work_unit" placeholder="Please enter your School/Workplace">
        </div>


        <div class="form-group">
            <label for="email">Email</label>
            <input type="text" id="email" name="email" placeholder="Please enter your email address">
        </div>

        <div class="form-group">
            <label for="education">Education level</label>
            <select id="education" name="education">
                <option value="">Please select</option>
                <option value="0">Elementary School/Primary School</option>
                <option value="1">Middle School/Junior High School</option>
                <option value="2">High School</option>
                <option value="3">Technical/Vocational School</option>
                <option value="4">College/University (Undergraduate)</option>
                <option value="5">Master's degree</option>
                <option value="6">Doctoral degree or higher</option>
            </select>
        </div>

        <div class="form-group">
            <label for="abroad">The duration of your residence in China: </label>
            <select id="abroad" name="abroad">
                <option value="">Please select</option>
                <option value="0">Never</option>
                <option value="1">Less than 6 months</option>
                <option value="2">6 months to 2 years</option>
                <option value="3">2 to 5 year</option>
                <option value="4">More than 5 years</option>
            </select>
        </div>

        <button type="submit" class="submit-button" id="submit-button">Complete Registration</button>
    </form>
</body>

</html>