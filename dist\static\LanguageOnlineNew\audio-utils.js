/**
 * Universal Audio Processing Utilities
 * Provides consistent audio conversion and upload functionality across all HTML files
 */

/**
 * Convert audio blob to WAV format
 * @param {Blob} blob - The audio blob to convert
 * @returns {Promise<Blob>} - Promise that resolves to WAV blob
 */
function blobToWav(blob) {
    return new Promise((resolve, reject) => {
        // If the blob is already in WAV format, return it directly
        if (blob.type === 'audio/wav' || blob.type === 'audio/wave') {
            resolve(blob);
            return;
        }
        
        // Create an audio context to decode the audio data
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        
        // Read the blob as an array buffer
        const reader = new FileReader();
        reader.onload = function() {
            // Decode the audio data
            audioContext.decodeAudioData(reader.result, function(buffer) {
                // Convert the decoded audio to WAV format
                const wavBlob = audioBufferToWav(buffer);
                resolve(wavBlob);
            }, function(error) {
                console.error('Error decoding audio data:', error);
                // If decoding fails, just return the original blob with .wav extension
                resolve(new Blob([blob], { type: 'audio/wav' }));
            });
        };
        
        reader.onerror = function() {
            console.error('Error reading blob');
            reject(new Error('Failed to read audio data'));
        };
        
        reader.readAsArrayBuffer(blob);
    });
}

/**
 * Convert AudioBuffer to WAV format
 * @param {AudioBuffer} buffer - The audio buffer to convert
 * @returns {Blob} - WAV blob
 */
function audioBufferToWav(buffer) {
    const numChannels = buffer.numberOfChannels;
    const sampleRate = buffer.sampleRate;
    const format = 1; // PCM
    const bitDepth = 16;
    
    // Create a buffer for the WAV file
    const result = new ArrayBuffer(44 + buffer.length * numChannels * 2);
    const view = new DataView(result);
    
    // Write the WAV header
    writeString(view, 0, 'RIFF');
    view.setUint32(4, 36 + buffer.length * numChannels * 2, true);
    writeString(view, 8, 'WAVE');
    writeString(view, 12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, format, true);
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numChannels * bitDepth / 8, true);
    view.setUint16(32, numChannels * bitDepth / 8, true);
    view.setUint16(34, bitDepth, true);
    writeString(view, 36, 'data');
    view.setUint32(40, buffer.length * numChannels * 2, true);
    
    // Write the PCM data
    let offset = 44;
    for (let i = 0; i < buffer.length; i++) {
        for (let channel = 0; channel < numChannels; channel++) {
            const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
            view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
            offset += 2;
        }
    }
    
    return new Blob([result], { type: 'audio/wav' });
}

/**
 * Helper function to write string to DataView
 * @param {DataView} view - The DataView to write to
 * @param {number} offset - The offset to start writing at
 * @param {string} string - The string to write
 */
function writeString(view, offset, string) {
    for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
    }
}

/**
 * Universal audio upload function
 * @param {Blob} blob - The audio blob to upload
 * @param {string} idNumber - User ID number
 * @param {string|number} taskId - Task ID
 * @param {string} filePrefix - File prefix (e.g., 'vft', 'dis', 'tat')
 * @param {string|number} itemId - Item ID (question index, picture ID, etc.)
 * @param {HTMLElement} statusElement - Status text element to update
 * @returns {Promise} - Upload promise
 */
function uploadAudioUniversal(blob, idNumber, taskId, filePrefix, itemId, statusElement) {
    console.log('Uploading audio to server');
    const timestamp = Math.floor(Date.now() / 1000);
    
    // Convert blob to WAV format first
    return blobToWav(blob).then(wavBlob => {
        console.log("Converted WAV Blob size:", wavBlob.size);
        console.log("Converted WAV Blob type:", wavBlob.type);
        
        // Generate standardized filename
        const audioFileName = `${idNumber}_${filePrefix}${taskId}_${timestamp}.wav`;
        console.log("Generated filename:", audioFileName);
        
        // Create FormData for upload
        const formData = new FormData();
        formData.append('file', wavBlob, audioFileName);
        formData.append('idNumber', idNumber);
        formData.append('taskId', taskId);
        formData.append('timestamp', timestamp);
        
        console.log("FormData prepared, sending to server...");
        if (statusElement) {
            statusElement.textContent = "Uploading to server...";
        }
        
        return fetch('/upload', {
            method: 'POST',
            body: formData
        }).then(async (resp) => {
            if (!resp.ok) {
                const text = await resp.text().catch(() => '');
                throw new Error(`HTTP ${resp.status}: ${text}`);
            }
            return resp.json();
        }).then((result) => {
            if (statusElement) {
                statusElement.textContent = "Successfully saved";
            }
            console.log('upload success', result);
            return result;
        }).catch((error) => {
            console.error('Upload failed:', error);
            
            // Fallback to localStorage if server upload fails
            console.log('Falling back to localStorage');
            const reader = new FileReader();
            return new Promise((resolve, reject) => {
                reader.onload = function() {
                    const audioData = {
                        name: audioFileName,
                        data: reader.result, // base64 data
                        timestamp: timestamp,
                        taskId: taskId,
                        idNumber: idNumber,
                        filePrefix: filePrefix,
                        itemId: itemId
                    };
                    
                    // Save to localStorage
                    localStorage.setItem(audioFileName, JSON.stringify(audioData));
                    console.log('Audio saved to localStorage with key:', audioFileName);
                    
                    // Update status to show local save was successful
                    if (statusElement) {
                        statusElement.textContent = "Saved locally. You can try uploading again later.";
                    }
                    
                    resolve(audioFileName);
                };
                reader.onerror = function() {
                    console.error('Error reading blob');
                    if (statusElement) {
                        statusElement.textContent = "Error: Could not save audio locally.";
                    }
                    reject(new Error('Failed to read audio data'));
                };
                reader.readAsDataURL(wavBlob);
            });
        });
    });
}
