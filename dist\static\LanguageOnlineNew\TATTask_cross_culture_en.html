<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <title>Discourse</title>
    <link rel="stylesheet" href="dis_crossculture.css">
    <script src="audio-utils.js"></script>


    <style>
        .top {
        }
        .left {
            display: inline-block;
            width: 45%;
        }
        .right{
            display: inline-block;
            width: 45%
        }
        .right > img{
            width: 95%;
        }
        @media (max-aspect-ratio: 1.2)  {
            .left {
                display: inline-block;
                width: 100%;
            }
            .right{
                display: inline-block;
                width: 100%
            }
            .right > img{
                width: 95%;
            }
        }

        /* 添加样式以美化可视化画布 */
        #audioVisualizer {
            width: 100%;
            height: 100px;
            background-color: #f3f3f3;
            border: 1px solid #ccc;
            margin-top: 20px;
            display: none;
            /* 初始隐藏，录音时显示 */
        }
    </style>
</head>

<body>
<div class="progress-container" >
    <div class="progress-bar" id="progress_bar" style="width:70%;">70%</div>
</div>

    <div class="container">


        <div class="top">
            <div id="ENCN" class="en-cn"></div>
            <div class="left">
                <p id="questionText" class="question"  style='text-align: left'>
                    请告诉我您在图片中<span class="highlight">看到了什么</span><br>
                    请在准备好回答之后点击“开始录音”（回答时间需要超过<span class="key-point">30秒</span>）
                </p>
            </div>
            <div class="right">
                <img id="TATImage" src="" alt="描述图片内容" width="500">
            </div>
        </div>

        <div class="bottom" style=" text-align: center">
            <!-- <button id="startRecord" class="btn" style="display: none">Start</button> -->
            <button id="startRecord" class="btn" style="width:45%">Start</button>

            <button id="stopRecord" class="btn" style="display: none" disabled style="width:45%">Stop</button>
            <button id="nextQuestion" class="btn" disabled style="width:45%">Next task</button>

            <audio id="audio" controls class="audio-player"></audio>
            <p id="status" class="status-text">Awaiting recording</p>

            <!-- 在 body 中的合适位置添加 -->
            <div id="savingStatus" style="display: none;">正在保存...</div>
        </div>


        <!-- <button id="skipQuestion" class="btn" style="display:none">跳过</button> -->
        <button id="reUpload" style="display:none" class="btn" disabled>Upload(retry)</button>
        <button id="reskip" style="display:none" class="btn" disabled>Next task(retry)</button>

        <!-- 添加音频可视化画布 -->
        <canvas id="audioVisualizer" width="600" height="100"></canvas>
    </div>

    <script>
        let stopButtonTimeout; //防止定时器出问题，及时销毁
        hook_on_img = document.getElementById("TATImage");
        hook_on_img.onload = function(){
            startRecord.style.display = "";
            stopRecord.style.display="none";

            console.log("图片加载完成", hook_on_img.src);
            var thinkingDuration=30;
            var interval_id=setInterval(()=>{
                thinkingDuration--;
                statusText.textContent= "Thinking ｜ Rest：" + thinkingDuration + " sec";
            }, 1000);
            startRecord.disabled = true;
            setTimeout(() => {
                startRecord.disabled = false;
                
                // startRecord.click();
                clearInterval(interval_id);
                // startRecord.textContent="Recording"
            }, 1000* thinkingDuration);
        }

        var idNumber = decodeURIComponent(location.search.match(/[^\?&]*idNumber=([^&]*)/)[1]);
        //添加新的图片只需要ID并添加编号
        var TATPic_ID_Initial = [0, 1, 2, 3, 4, 5, 6, 7,
            8, 9, 10, 11, 12, 13, 14, 15];
        // 中文题目对应图片
        let TATPicture = [{ Name: "./TATPic_cross_culture/culture1.webp" },
        { Name: "./TATPic_cross_culture/culture2.webp" },
        { Name: "./TATPic_cross_culture/family1.webp" },
        { Name: "./TATPic_cross_culture/family2.webp" },
        { Name: "./TATPic_cross_culture/self1.webp" },
        { Name: "./TATPic_cross_culture/self2.webp" },
        { Name: "./TATPic_cross_culture/social1.webp" },
        { Name: "./TATPic_cross_culture/social2.webp" },
        // 中文题目对应图片，判断需要大于7都是中文题目
        { Name: "./TATPic_cross_culture/culture1.webp" },
        { Name: "./TATPic_cross_culture/culture2.webp" },
        { Name: "./TATPic_cross_culture/family1.webp" },
        { Name: "./TATPic_cross_culture/family2.webp" },
        { Name: "./TATPic_cross_culture/self1.webp" },
        { Name: "./TATPic_cross_culture/self2.webp" },
        { Name: "./TATPic_cross_culture/social1.webp" },
        { Name: "./TATPic_cross_culture/social2.webp" }
        ];

        let common_question=[
            "What might have happened before?",
            "What is happening now?",
            "What could happen next?",
            "What are the characters thinking or feeling? Why?"
        ]

        let time_constraint = 120;

        let questions_cn = "<span style='font-size: 20px'>This task requires you to answer <span style='color: #d9534f; font-size:30px'><strong>In CHINESE</strong></span>.</span>"

        let questions_en = "<span style='font-size: 20px'>This task requires you to answer <span style='color: #CDBE70; font-size:30px'><strong>In ENGLISH</strong></span>.</span>"

        //读取存储的currentIndex0804,可以刷新
        var currentPicIndex = JSON.parse(sessionStorage.getItem('currentPicIndex_storage'));
        if (currentPicIndex) {
            console.log('Read index successfully');
        } else {
            var currentPicIndex = 0;
            console.log('Initial currentIndex');
        }

        let startRecord = document.getElementById('startRecord');
        let stopRecord = document.getElementById('stopRecord');
        let audio = document.getElementById('audio');
        let statusText = document.getElementById('status');
        let nextQuestion = document.getElementById('nextQuestion');
        let TATImage = document.getElementById("TATImage");
        let questionText = document.getElementById("questionText");
        // let skipQuestion = document.getElementById('skipQuestion');
        let reUpload = document.getElementById('reUpload');
        let reskip = document.getElementById('reskip');
        let mediaRecorder;
        let audioChunks = [];
        let recordingDuration = 0;
        let recordingInterval;
        // let skip_onclicked = false;

        let progress_bar=document.getElementById('progress_bar');
        let encn=document.getElementById('ENCN');

        // 音频可视化相关变量
        let audioVisualizer = document.getElementById('audioVisualizer');
        let canvasCtx = audioVisualizer.getContext('2d');
        let audioContext;
        let analyser;
        let source;
        let animationId;

        //图片呈现顺序随机化
        //0804可刷新版本
        var TATPic_ID = JSON.parse(sessionStorage.getItem('TATPic_ID_storage'));
        if (TATPic_ID) {
            console.log('Read index successfully');
        } else {
            var TATPic_ID = shuffleArray(TATPic_ID_Initial);
            sessionStorage.setItem('TATPic_ID_storage', JSON.stringify(TATPic_ID));
            console.log('Initial TAT');
        }
        showPicQuestion()
        function showPicQuestion() {
            // 如果有之前的停止按钮计时器，清除它
            if (stopButtonTimeout) {
                clearTimeout(stopButtonTimeout);
            }

            if (currentPicIndex < TATPic_ID.length) {
                let width_progress = 100 * (currentPicIndex + 1) / TATPic_ID.length;
                progress_bar.style.width = width_progress + '%';
                progress_bar.innerHTML = `${currentPicIndex + 1}/${TATPic_ID.length}`;
            }

            if (TATPic_ID[currentPicIndex] > 7) {
                questions = questions_cn;
            } else {
                questions = questions_en;

            }

            // 如果已经显示了所有词语，就不再显示
            if (currentPicIndex >= TATPic_ID.length) {
                window.location.href = 'cross_culture_end_en.html?idNumber=' + encodeURIComponent(idNumber);  // [change]
                return;
            }
            skip_onclicked = false;
            startRecord.disabled = false;

            var real_question=shuffleArray(common_question);
            var display_question="<div>"
            for(var i=0;i<common_question.length;i++){
                display_question+="<p>"+(i+1)+". "+real_question[i]+"</p>"
            }
            display_question+="</div>"

            encn.innerHTML = questions;
            var common_intro="<p>We would like you to <span class='key-point'>narrate a complete story</span> for each image, including the following details:</p>"
            questionText.innerHTML =  common_intro+ display_question+
                '</span><br><span style="font-size: 15px;">Organize your answer at least <span class="key-point">30</span> seconds. Speak for at least <span class="key-point">' + time_constraint + ' seconds</span>, you\'ll be prompted when you can stop.</span>';
            TATImage.src = TATPicture[TATPic_ID[currentPicIndex]].Name;
            console.log("currentPicIndex", currentPicIndex);
            console.log("TATPic_ID[currentPicIndex]", TATPic_ID[currentPicIndex]);
            console.log("TATPicture[TATPic_ID[currentPicIndex]]", TATPicture[TATPic_ID[currentPicIndex]]);

            startRecord.textContent = "Start"; // 更改按钮文本





            nextQuestion.disabled = true;
        }

        document.addEventListener('DOMContentLoaded', function () {
            // 设置 skipQuestion 按钮初始状态为不可点击
            // skipQuestion.disabled = true;
            startRecord.onclick = function () {
                stopRecord.style.display=""
                stopRecord.style.width="45%"
                startRecord.style.display="none"
                audioChunks = [];
                audio.src = '';
                //隐藏录音进度条
                audio.style.display = 'none';
                // skipQuestion.disabled = false;
                navigator.mediaDevices.getUserMedia({ audio: true })
                    .then(stream => {
                        let options = {};
                        if (MediaRecorder.isTypeSupported('audio/webm')) {
                            options.mimeType = 'audio/webm';
                        } else {
                            console.log('Using browser default audio format.');
                        }
                        mediaRecorder = new MediaRecorder(stream, options);

                        mediaRecorder.ondataavailable = function (e) {
                            audioChunks.push(e.data);
                        };
                        mediaRecorder.onstop = function () {

                            if (stopButtonTimeout) {
                                clearTimeout(stopButtonTimeout);
                            }
            
                            let audioBlob = new Blob(audioChunks, { type: mediaRecorder.mimeType });
                            console.log("Recorded Blob size:", audioBlob.size); // Debugging: Check Blob size
                            //console.log("Used MIME type:", mediaRecorder.mimeType); // Debugging: Check used MIME type
                            let audioUrl = URL.createObjectURL(audioBlob);
                            audio.src = audioUrl;
                            clearInterval(recordingInterval);

                            // 停止可视化
                            if (animationId) {
                                cancelAnimationFrame(animationId);
                            }
                            if (audioContext) {
                                audioContext.close();
                            }
                            audioVisualizer.style.display = 'none';


                            if (skip_onclicked == true) {
                                skipQuestion.disabled = true;
                                startRecord.disabled = true;
                                stopRecord.disabled = true;
                                statusText.textContent = "录音已停止";
                                nextQuestion.disabled = true;
                                reskip.disabled = true;
                                reUpload.disabled = true;
                                // 显示录音进度条
                                audio.style.display = 'block';
                                let audioBlob = new Blob(audioChunks, { type: mediaRecorder.mimeType });
                                console.log("Recorded Blob size:", audioBlob.size); // Debugging: Check Blob size
                                uploadAudio(audioBlob).then(showPicQuestion);
                            }
                        };
                        mediaRecorder.start();

                        recordingDuration = 0;
                        recordingInterval = setInterval(function () {
                            recordingDuration++;
                            statusText.textContent = "Recording ｜ Time：" + recordingDuration + " sec";

                        }, 1000);
                        stopRecord.innerText= 'Recording';

                        stopButtonTimeout = setTimeout(()=>{
                                stopRecord.disabled = false;
                                stopRecord.innerHTML= 'Stop';
                            },time_constraint*1000);

                        startRecord.disabled = true;
                        // startRecord.textContent = "重新录音";
                        nextQuestion.disabled = true;
                        statusText.textContent = "Recording started";

                        // 初始化音频可视化
                        audioVisualizer.style.display = 'block';
                        audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        source = audioContext.createMediaStreamSource(stream);
                        analyser = audioContext.createAnalyser();
                        analyser.fftSize = 2048;
                        source.connect(analyser);

                        const bufferLength = analyser.frequencyBinCount;
                        const dataArray = new Uint8Array(bufferLength);

                        // 设置 canvas 的实际尺寸，以匹配 CSS 尺寸
                        audioVisualizer.width = audioVisualizer.clientWidth;
                        audioVisualizer.height = audioVisualizer.clientHeight;

                        // 清除画布
                        canvasCtx.clearRect(0, 0, audioVisualizer.width, audioVisualizer.height);

                        function drawVisualizer() {
                            animationId = requestAnimationFrame(drawVisualizer);

                            analyser.getByteTimeDomainData(dataArray);

                            // 绘制背景
                            canvasCtx.fillStyle = '#f3f3f3';
                            canvasCtx.fillRect(0, 0, audioVisualizer.width, audioVisualizer.height);

                            // 绘制波形
                            canvasCtx.lineWidth = 2;
                            canvasCtx.strokeStyle = '#d9534f';

                            canvasCtx.beginPath();

                            const sliceWidth = audioVisualizer.width * 1.0 / bufferLength;
                            let x = 0;

                            for (let i = 0; i < bufferLength; i++) {
                                const v = dataArray[i] / 128.0;
                                const y = v * audioVisualizer.height / 2;

                                if (i === 0) {
                                    canvasCtx.moveTo(x, y);
                                } else {
                                    canvasCtx.lineTo(x, y);
                                }

                                x += sliceWidth;
                            }

                            canvasCtx.lineTo(audioVisualizer.width, audioVisualizer.height / 2);
                            canvasCtx.stroke();
                        }

                        drawVisualizer();
                    });
            };

            //

            // skipQuestion.onclick = function () {
            //     skip_onclicked = true;
            //     mediaRecorder.stop();
            // }


            //
            stopRecord.onclick = function () {
                if (recordingDuration < time_constraint) {
                    alert("The recording time is longer than " + time_constraint + " seconds, come on, please speak more.");
                    return;
                }
                mediaRecorder.stop();
                // skipQuestion.disabled = true;
                // startRecord.disabled = false;
                stopRecord.disabled = true;
                statusText.textContent = "Recording has stopped";
                nextQuestion.disabled = false;
                //显示录音进度条
                audio.style.display = 'block';
            };

            nextQuestion.onclick = function () {
                nextQuestion.disabled = true;
                let audioBlob = new Blob(audioChunks, { type: mediaRecorder.mimeType });
                console.log("Recorded Blob size:", audioBlob.size); // Debugging: Check Blob size
                uploadAudio(audioBlob).then(showPicQuestion);

                statusText.textContent='Saving...';
            };

            reskip.onclick = function () {
                startRecord.disabled = false;
                reskip.disabled = true;
                reskip.style='display: none';
                reUpload.style='display: none';

                // 循环结束
                if (currentPicIndex == TATPic_ID.length - 1) {
                    window.location.href = 'cross_culture_end_en.html?idNumber=' + encodeURIComponent(idNumber);
                }
                // 换图片
                if (currentPicIndex < TATPic_ID.length - 1) {
                    currentPicIndex++;
                    sessionStorage.setItem('currentPicIndex_storage', currentPicIndex);//0804
     
                    if (TATPic_ID[currentPicIndex] > 7) {
                        questions = questions_cn;
                    } else {
                        questions = questions_en;

                    }

                    var real_question=shuffleArray(common_question);
                    var display_question="<div>"
                    for(var i=0;i<common_question.length;i++){
                        display_question+="<p>"+(i+1)+". "+real_question[i]+"</p>"
                    }
                    display_question+="</div>"

                    encn.innerHTML = questions;
                    var common_intro="<p>We would like you to <span class='key-point'>narrate a complete story</span> for each image, including the following details:</p>"
                    questionText.innerHTML =  common_intro+ display_question+
                        '</span><br><span style="font-size: 15px;">Recording begins in 60 seconds. Speak for at least <span class="key-point">' + time_constraint + ' seconds</span>, you\'ll be prompted when you can stop.</span>';
                    TATImage.src = TATPicture[TATPic_ID[currentPicIndex]].Name;
                    console.log("currentPicIndex", currentPicIndex);
                    console.log("TATPic_ID[currentPicIndex]", TATPic_ID[currentPicIndex]);
                    console.log("TATPicture[TATPic_ID[currentPicIndex]]", TATPicture[TATPic_ID[currentPicIndex]]);
                }
                if (currentPicIndex < TATPic_ID.length) {
                    let width_progress = 100 * (currentPicIndex + 1) / TATPic_ID.length;
                    progress_bar.style.width = width_progress + '%';
                    progress_bar.innerHTML = `${currentPicIndex + 1}/${TATPic_ID.length}`;
                }
            };

            reUpload.onclick = function () {
                let audioBlob = new Blob(audioChunks, { type: (mediaRecorder && mediaRecorder.mimeType) ? mediaRecorder.mimeType : 'audio/webm' });
                nextQuestion.disabled = true;
                reUpload.disabled = true;
                statusText.textContent = 'Saving...';
                uploadAudio(audioBlob).then(() => {
                    reskip.disabled = false;
                }).catch(() => {
                    reUpload.disabled = false;
                });
            };

            // Upload audio to local Flask server
            function uploadAudio(blob) {
                nextQuestion.disabled = true;
                console.log('Uploading audio to server');
                console.log("Uploading Blob size:", blob.size);

                // Use the universal audio upload function
                return uploadAudioUniversal(
                    blob,
                    idNumber,
                    String(TATPic_ID[currentPicIndex] + 1),
                    'tat',
                    TATPic_ID[currentPicIndex],
                    statusText
                ).then((result) => {
                    audio.style.display = 'none';
                    console.log('upload success', result);
                    stopRecord.disabled = true;
                    nextQuestion.disabled = true;
                    startRecord.textContent = "Start";
                    nextQuestion.disabled = true;

                    // End of sequence
                    if (currentPicIndex == TATPic_ID.length - 1) {
                        window.location.href = 'cross_culture_end_en.html?idNumber=' + encodeURIComponent(idNumber);
                    }
                    // Advance to next image
                    if (currentPicIndex < TATPic_ID.length - 1) {
                        currentPicIndex++;
                        sessionStorage.setItem('currentPicIndex_storage', currentPicIndex);//0804
                        if (TATPic_ID[currentPicIndex] > 7) {
                            questions = questions_cn;
                        } else {
                            questions = questions_en;
                        }

                        var real_question = shuffleArray(common_question);
                        var display_question = "<div>";
                        for (var i = 0; i < common_question.length; i++) {
                            display_question += "<p>" + (i + 1) + ". " + real_question[i] + "</p>";
                        }
                        display_question += "</div>";

                        var common_intro = "<p>We would like you to <span class='key-point'>narrate a complete story</span> for each image, including the following details:</p>";
                        questionText.innerHTML = common_intro + display_question +
                            '</span><br><span style="font-size: 15px;">Click "Start" and speak for at least <span class="key-point">' + time_constraint + ' seconds</span>. Once the time limit is reached, the system will allow you to stop the recording.</span>';

                        TATImage.src = TATPicture[TATPic_ID[currentPicIndex]].Name;
                    }

                    // Success
                    reUpload.disabled = true;
                    nextQuestion.disable = true;
                    return result;
                }).catch((e) => {
                    statusText.textContent = 'Failed to save';
                    console.warn(e);
                    reUpload.disabled = false;
                    reUpload.style = 'display: inline-block'
                    reskip.style = 'display: inline-block'
                    throw e;
                });
            }


            function getFileExtension(mimeType) {
                switch (mimeType) {
                    case 'audio/webm':
                        return '.webm';
                    case 'audio/ogg':
                        return '.ogg';
                    // 添加其他支持的格式
                    default:
                        return '.wav'; // 默认扩展名
                }
            }
        });
        //0804保存进度版本（可刷新）
        function shuffleArray(array) {
            let newArr = [...array]; // 创建原数组的一个副本，避免改变原数组
            for (let i = newArr.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [newArr[i], newArr[j]] = [newArr[j], newArr[i]]; // 在副本上进行元素交换
            }
            return newArr; // 返回新的打乱后的数组
        }


    </script>

</body>

</html>