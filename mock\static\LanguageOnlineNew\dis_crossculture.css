
        body {
            font-family: 'Arial', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
        }
    
        .container {
            text-align: center;
            background: white;
            padding: 5%;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            max-width: 1000px;
            width: 800px;
            margin: 20px auto;
        }
    
        .btn {
            background-color: #007bff;
            color: white;
            padding: 15px 30px;
            margin: 15px 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 20px;
            transition: background-color 0.3s, transform 0.3s, box-shadow 0.3s;
        }
    
        .btn:hover {
            background-color: #0056b3;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
    
        .btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

    
        .audio-player {
            width: 100%;
            margin-top: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            display: none;/*让录音控件隐藏*/
        }
    
        .status-text {
            color: #666;
            margin-top: 25px;
            font-size: 16px;
        }
    
        .question {
            font-size: 20px;
            margin-bottom: 10px;
        }
        .en-cn{
            border: 2px solid black;
            /*text-shadow: 2px 2px 4px #999999;*/
        }
        .highlight {
            color: #007bff; /* 或者您喜欢的颜色 */
            font-weight: bold;
            /*font-size: 50px;*/
        }
        .instruction {
            font-size: 18px; /* 调整为合适的大小 */
            margin-bottom: 10px;
        }
        .key-point {
            font-size: 20px; /* 更大的字体大小 */
            font-weight: bold;
            color: #d9534f; /* 突出显示的颜色 */
        }

        ul {
            text-align: left;
            margin-top: 10px;
        }
        .icon {
            font-family: 'Font Awesome 5 Free'; /* 使用Font Awesome图标库 */
            font-weight: 900;
            font-size: 1em;
        }
        .instruction img {
            height: 18px; /* 将图像高度设置为18像素 */
            vertical-align: middle;
        }
        
        #imageGallery {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px; /* 图片之间的间距 */
            padding: 10px;
        }
        #imageGallery img {
            width: calc(100% / 5 - 10px); /* 5张图片一行，减去间距 */
            max-width: none; /* 覆盖之前的最大宽度设置 */
            transition: transform 0.3s ease; /* 平滑变换效果 */
            display: none; /* 默认不显示图片 */
        }
        #imageGallery img:hover {
            transform: scale(3); /* 鼠标悬停时放大 */
            z-index: 10; /* 确保放大图片在最上层 */
        }

        .progress-container {
            width: 100%;
            background-color: #f1f1f1;
            position: fixed;
            top: 0;
            height: 2.5%;
        }
        .progress-bar {
            height: 100%;
            background-color: #4CAF50;
            /*line-height: 3%;*/
            color: white;
            text-align: right;
        }


