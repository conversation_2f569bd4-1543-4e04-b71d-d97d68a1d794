#!/usr/bin/env python3
"""
Test script to verify the fixes applied to the audio upload system
"""

import os
import sys
import re
import requests
import time
from pathlib import Path

def test_server_route_fix():
    """Test that the server route is fixed (single slash instead of double slash)"""
    print("Testing server route fix...")
    
    # Read server.py file
    with open('server.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for the fixed route
    if "@app.route('/<path:filename>')" in content:
        print("✓ Server route fix verified: Single slash route found")
        return True
    elif "@app.route('//<path:filename>')" in content:
        print("✗ Server route fix failed: Double slash route still present")
        return False
    else:
        print("? Server route not found in expected format")
        return False

def test_audio_utils_integration():
    """Test that audio-utils.js is properly integrated into HTML files"""
    print("\nTesting audio-utils.js integration...")
    
    html_files = [
        'static/LanguageOnlineNew/vft_seq_cross_culture_en.html',
        'static/LanguageOnlineNew/dis_seq_cross_culture_en.html',
        'static/LanguageOnlineNew/TATTask_cross_culture_en.html'
    ]
    
    success = True
    for html_file in html_files:
        if os.path.exists(html_file):
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'audio-utils.js' in content:
                print(f"✓ {html_file}: audio-utils.js script tag found")
            else:
                print(f"✗ {html_file}: audio-utils.js script tag missing")
                success = False
        else:
            print(f"? {html_file}: File not found")
            success = False
    
    return success

def test_unified_upload_function():
    """Test that HTML files use the unified uploadAudioUniversal function"""
    print("\nTesting unified upload function usage...")
    
    html_files = [
        'static/LanguageOnlineNew/dis_seq_cross_culture_en.html',
        'static/LanguageOnlineNew/TATTask_cross_culture_en.html'
    ]
    
    success = True
    for html_file in html_files:
        if os.path.exists(html_file):
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'uploadAudioUniversal' in content:
                print(f"✓ {html_file}: Uses uploadAudioUniversal function")
            else:
                print(f"✗ {html_file}: Does not use uploadAudioUniversal function")
                success = False
        else:
            print(f"? {html_file}: File not found")
            success = False
    
    return success

def test_standardized_naming():
    """Test that the server supports standardized file naming"""
    print("\nTesting standardized file naming...")
    
    # Read server.py file
    with open('server.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for prefix extraction logic
    if 'file_prefix' in content and 're.match' in content:
        print("✓ Server supports dynamic file prefix extraction")
        return True
    else:
        print("✗ Server does not support dynamic file prefix extraction")
        return False

def test_audio_utils_file():
    """Test that audio-utils.js file exists and contains required functions"""
    print("\nTesting audio-utils.js file...")
    
    audio_utils_path = 'static/LanguageOnlineNew/audio-utils.js'
    if not os.path.exists(audio_utils_path):
        print("✗ audio-utils.js file not found")
        return False
    
    with open(audio_utils_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_functions = [
        'blobToWav',
        'audioBufferToWav',
        'writeString',
        'uploadAudioUniversal'
    ]
    
    success = True
    for func in required_functions:
        if f'function {func}' in content:
            print(f"✓ Function {func} found in audio-utils.js")
        else:
            print(f"✗ Function {func} missing from audio-utils.js")
            success = False
    
    return success

def test_readme_update():
    """Test that README.md has been updated with new naming convention"""
    print("\nTesting README.md update...")
    
    with open('README.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    if 'filePrefix' in content and 'vft, dis, tat' in content:
        print("✓ README.md updated with standardized naming convention")
        return True
    else:
        print("✗ README.md not updated with standardized naming convention")
        return False

def main():
    """Run all tests"""
    print("Running tests for audio upload system fixes...\n")
    
    tests = [
        test_server_route_fix,
        test_audio_utils_integration,
        test_unified_upload_function,
        test_standardized_naming,
        test_audio_utils_file,
        test_readme_update
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with error: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The fixes have been successfully applied.")
        return 0
    else:
        print("❌ Some tests failed. Please review the issues above.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
