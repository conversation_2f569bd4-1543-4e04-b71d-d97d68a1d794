# 部署说明 (Deployment Instructions)

## 问题解决方案

### 原问题
构建打包出exe后，exe找不到welcome文件（Welcome_cross_culture_en.html）。

### 解决方案
修改了构建配置，使data和static文件夹位于exe文件旁边，而不是打包到exe内部。

## 构建步骤

1. 运行构建脚本：
   ```bash
   build_exe.bat
   ```

2. 构建完成后，dist目录结构如下：
   ```
   dist/
   ├── server.exe          # 主程序
   └── static/            # 静态文件夹（HTML、JS等）
       ├── Welcome_cross_culture_en.html
       ├── Informed_Consent_cross_culture_en.html
       ├── Inform_cross_culture_en.html
       ├── Bmob.js
       └── LanguageOnlineNew/
   ```

   注意：data文件夹会在服务器首次运行时自动创建。

## 部署说明

### 完整部署包
将整个dist文件夹复制到目标机器，确保以下文件结构：
- server.exe
- static/ 文件夹

注意：data/ 文件夹会在服务器首次运行时自动创建，用于存储上传的音频文件。

### 运行程序
双击 `server.exe` 或在命令行中运行：
```bash
cd dist
server.exe
```

## 技术细节

### 路径解析机制
程序使用智能路径解析：
- **开发环境**：从当前工作目录查找data和static文件夹
- **打包环境**：从exe文件所在目录查找data和static文件夹

### 关键代码修改
1. **build_exe.py**：
   - 移除了 `--add-data` 参数
   - 添加了 `copy_resources()` 函数自动复制static文件夹
   - data文件夹不复制，由程序运行时创建

2. **server.py**：
   - 添加了 `get_resource_path()` 函数
   - 使用 `sys.frozen` 检测是否为打包环境
   - 动态解析data和static文件夹路径

### 优势
1. **易于维护**：可以直接修改static文件夹中的HTML、CSS、JS文件
2. **灵活部署**：不需要重新打包就能更新静态资源
3. **清晰结构**：文件组织更加直观
4. **调试友好**：可以直接查看和修改配置文件
5. **数据独立**：data文件夹运行时创建，避免覆盖现有数据

## 测试验证

运行测试脚本验证路径解析：
```bash
python test_paths.py
```

该脚本会显示：
- 当前运行环境（开发/打包）
- 解析的文件路径
- 文件存在性检查
