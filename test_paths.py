#!/usr/bin/env python3
"""
Test script to verify path resolution for both development and executable environments
"""

import os
import sys

def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    if getattr(sys, 'frozen', False):
        # Running as compiled executable
        # Get the directory where the executable is located
        base_path = os.path.dirname(sys.executable)
    else:
        # Running as script
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

def main():
    print("Path Resolution Test")
    print("=" * 50)
    print(f"sys.frozen: {getattr(sys, 'frozen', False)}")
    print(f"sys.executable: {sys.executable}")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Script directory: {os.path.dirname(os.path.abspath(__file__))}")
    print()
    
    # Test paths
    data_path = get_resource_path('data')
    static_path = get_resource_path('static')
    welcome_file = os.path.join(static_path, 'Welcome_cross_culture_en.html')
    
    print("Resolved Paths:")
    print(f"Data folder: {data_path}")
    print(f"Static folder: {static_path}")
    print(f"Welcome file: {welcome_file}")
    print()
    
    # Check if paths exist
    print("Path Existence Check:")
    print(f"Data folder exists: {os.path.exists(data_path)}")
    print(f"Static folder exists: {os.path.exists(static_path)}")
    print(f"Welcome file exists: {os.path.exists(welcome_file)}")
    
    if os.path.exists(static_path):
        print(f"Static folder contents: {os.listdir(static_path)}")

if __name__ == "__main__":
    main()
